<!-- Plugin description -->
# TA+3 New Coder IntelliJ Plugin

## Introduction
TA+3 New Coder is an intelligent coding assistance plugin for IntelliJ IDEA. It aims to improve coding efficiency, reduce repetitive work, and help new programmers learn and grow faster.

## Key Features
- Intelligent code completion
- Context-aware code suggestions
- Real-time code quality checks
- Automatic refactoring suggestions
- Learning mode: Providing detailed code explanations and best practices for new programmers

## Installation
1. Open IntelliJ IDEA
2. Go to `File` > `Settings` > `Plugins`
3. Click the `Marketplace` tab
4. Search for "TA+3 New Coder"
5. Click `Install`

## Usage
- The plugin will automatically provide intelligent suggestions while you code
- Hover over code to view detailed explanations
- Use shortcuts to quickly trigger code suggestions and refactoring

## Support
If you encounter any issues, please visit our GitHub repository to submit an issue.

## License
[Add license information here]

## Version
v1.0.0

<!-- Plugin description end -->

## 中文说明

### 插件简介
TA+3 New Coder 是一个为开发者提供智能编码辅助的 IntelliJ IDEA 插件。它旨在提高编码效率，减少重复性工作，并帮助新手程序员更快地学习和成长。

### 主要特性
- 智能代码补全
- 上下文相关的代码建议
- 实时代码质量检查
- 自动重构建议
- 学习模式：为新手程序员提供详细的代码解释和最佳实践指导

### 安装步骤
1. 打开 IntelliJ IDEA
2. 进入 `文件` > `设置` > `插件`
3. 点击 `市场` 标签
4. 搜索 "TA+3 New Coder"
5. 点击 `安装`

### 使用方法
- 插件将在您编码时自动提供智能建议
- 将鼠标悬停在代码上可查看详细解释
- 使用快捷键快速触发代码建议和重构

### 技术支持
如遇到任何问题，请访问我们的 GitHub 仓库提交 issue。

### 许可证
[在此添加许可证信息]

### 版本
v1.0.0
